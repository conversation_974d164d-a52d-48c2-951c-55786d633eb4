import { useRef, useEffect, useState, useCallback } from 'react';

const Portfolio = () => {
  const portfolioItems = [
    {
      href: "https://threed-e-commerce.onrender.com",
      image: "/3D E-Comm.PNG",
      alt: "3D Ecommerce",
      title: "3D Ecommerce"
    },
    {
      href: "#",
      image: "/ex1.webp",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex2.png",
      alt: "Nexit Brand Identity",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex3.webp",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex4.1.png",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/ex5.png",
      alt: "Yalla Go Posters",
      title: "Will be deployed soon."
    },
    {
      href: "#",
      image: "/bussniss web UI.PNG",
      alt: "Business Web UI",
      title: "Available in git Will be deployed soon."
    }
  ];

  // Refs and state for carousel functionality
  const carouselRef = useRef(null);
  const trackRef = useRef(null);
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isAutoScrolling] = useState(true); // eslint-disable-line no-unused-vars
  const animationRef = useRef(null);

  // Auto-scroll functionality
  const autoScroll = useCallback(() => {
    if (!trackRef.current || isHovered || isDragging || !isAutoScrolling) return;

    const track = trackRef.current;
    const scrollAmount = 0.5; // Slower, smoother scrolling
    const maxScroll = track.scrollWidth / 2; // Half width (one set of items)

    track.scrollLeft += scrollAmount;

    // Reset to beginning when we've scrolled through one complete set
    if (track.scrollLeft >= maxScroll - 1) {
      track.scrollLeft = 1; // Start slightly offset
    }

    animationRef.current = requestAnimationFrame(autoScroll);
  }, [isHovered, isDragging, isAutoScrolling]);

  // Start auto-scroll
  useEffect(() => {
    const startAutoScroll = () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (isAutoScrolling && !isHovered && !isDragging) {
        animationRef.current = requestAnimationFrame(autoScroll);
      }
    };

    startAutoScroll();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [autoScroll, isAutoScrolling, isHovered, isDragging]);

  // Initialize auto-scroll on component mount
  useEffect(() => {
    const startInitialAutoScroll = () => {
      if (!trackRef.current || isHovered || isDragging || !isAutoScrolling) return;

      const track = trackRef.current;
      const scrollAmount = 0.5;
      const maxScroll = track.scrollWidth / 2;

      track.scrollLeft += scrollAmount;

      if (track.scrollLeft >= maxScroll - 1) {
        track.scrollLeft = 1;
      }

      animationRef.current = requestAnimationFrame(startInitialAutoScroll);
    };

    // Start the auto-scroll immediately
    animationRef.current = requestAnimationFrame(startInitialAutoScroll);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isHovered, isDragging, isAutoScrolling]); // Include necessary dependencies

  // Mouse event handlers for desktop drag functionality
  const handleMouseDown = (e) => {
    if (!trackRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - trackRef.current.offsetLeft);
    setScrollLeft(trackRef.current.scrollLeft);
    trackRef.current.style.cursor = 'grabbing';
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !trackRef.current) return;
    e.preventDefault();
    const x = e.pageX - trackRef.current.offsetLeft;
    const walk = (x - startX) * 2; // Multiply for faster scroll
    trackRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    if (trackRef.current) {
      trackRef.current.style.cursor = 'grab';
    }
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
    setIsHovered(false);
    if (trackRef.current) {
      trackRef.current.style.cursor = 'grab';
    }
  };

  // Touch event handlers for mobile swipe functionality
  const handleTouchStart = (e) => {
    if (!trackRef.current) return;
    setIsDragging(true);
    const touch = e.touches[0];
    setStartX(touch.clientX);
    setScrollLeft(trackRef.current.scrollLeft);
    // Prevent default to avoid conflicts with native scrolling
    e.preventDefault();
  };

  const handleTouchMove = (e) => {
    if (!isDragging || !trackRef.current) return;
    e.preventDefault(); // Prevent page scrolling
    const touch = e.touches[0];
    const walk = (startX - touch.clientX) * 1.5; // Adjust sensitivity
    trackRef.current.scrollLeft = scrollLeft + walk;
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  // Wheel event handler for mouse wheel scrolling
  const handleWheel = (e) => {
    if (!trackRef.current) return;

    // Only handle wheel events when hovering over the carousel
    // This prevents page scrolling when interacting with the carousel
    e.preventDefault();
    e.stopPropagation();

    // Convert vertical wheel movement to horizontal carousel scroll
    const scrollAmount = e.deltaY * 2; // Multiply for more responsive scrolling
    trackRef.current.scrollLeft += scrollAmount;
  };

  // Hover handlers for auto-scroll pause/resume
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeaveCarousel = () => {
    setIsHovered(false);
  };

  // Handle infinite loop reset
  useEffect(() => {
    const track = trackRef.current;
    if (!track) return;

    const handleScroll = () => {
      const maxScroll = track.scrollWidth / 2;
      const tolerance = 10; // Add tolerance to prevent flickering

      // If we've scrolled past the first set, reset to beginning
      if (track.scrollLeft >= maxScroll - tolerance) {
        track.scrollLeft = 1; // Start slightly offset to avoid immediate retrigger
      }
      // If we've scrolled before the beginning (manual scroll back), jump to end of first set
      else if (track.scrollLeft <= tolerance && !isAutoScrolling) {
        track.scrollLeft = maxScroll - track.clientWidth - tolerance;
      }
    };

    track.addEventListener('scroll', handleScroll, { passive: true });
    return () => track.removeEventListener('scroll', handleScroll);
  }, [isAutoScrolling]);

  return (
    <section className="portfolio">
      <h2>Top Projects<br /></h2>
      <button className="discover-button" onClick={() => console.log('Discover more clicked')}>DISCOVER MORE</button>
      <div
        className="portfolio-carousel"
        ref={carouselRef}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeaveCarousel}
        onWheel={handleWheel}
      >
        <div
          className="carousel-track"
          ref={trackRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
        >
          {/* Render items twice for infinite scroll */}
          {[...portfolioItems, ...portfolioItems].map((item, index) => (
            <div key={index} className="portfolio-item">
              <a href={item.href} target="_blank" rel="noopener noreferrer">
                <img src={item.image} alt={item.alt} />
                <p>{item.title}</p>
              </a>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Portfolio;
